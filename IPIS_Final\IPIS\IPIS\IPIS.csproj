﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>WinExe</OutputType>
    <TargetFramework>net8.0-windows</TargetFramework>
    <Nullable>enable</Nullable>
    <UseWindowsForms>true</UseWindowsForms>
    <ImplicitUsings>enable</ImplicitUsings>
  </PropertyGroup>

  <ItemGroup>
    <Compile Remove="context\**" />
    <EmbeddedResource Remove="context\**" />
    <None Remove="context\**" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="System.Data.SQLite" Version="1.0.119" />
    <PackageReference Include="System.Data.SQLite.Core" Version="1.0.119" />
    <PackageReference Include="System.Configuration.ConfigurationManager" Version="9.0.5" />
    <PackageReference Include="System.Drawing.Common" Version="9.0.5" />
  </ItemGroup>

</Project> 