using System;
using System.Windows.Forms;
using System.Drawing;
using IPIS.Utils;
using System.IO;
using IPIS.Services;
using IPIS.Repositories;
using System.Linq;
using System.Data;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace IPIS.Forms.Advertising
{
    public partial class AdvertisingForm : Form
    {
        private SplitContainer splitContainer;
        private DataGridView dgvAdvertising;
        private Panel detailsPanel;
        private Button btnAddAdvertising, btnEditAdvertising, btnDeleteAdvertising, btnRefresh;
        private readonly AdvertisingService advertisingService;
        private readonly ToastNotification toast;
        private TableLayoutPanel detailsTable;
        private ComboBox cmbAnnType, cmbLanguage, cmbPlayPosition;
        private TextBox txtAdverName, txtHindiWave, txtEngWave, txtTimeSlot, txtTrainNumber, txtRank, txtAdverTime, txtAdverCount;
        private NumericUpDown numMonthQuota, numDayQuota, numSlotQuota;
        private CheckBox chkRandomize;
        private Button btnHindiWave, btnEngWave, btnSave, btnCancel;
        private Label lblMode, lblQuota;
        private bool isEditMode = false;
        private DataRow editingRow = null;
        private FlowLayoutPanel languageWavePanel;
        private CheckedListBox clbTrainNumbers;
        private TableLayoutPanel quotasPanel;
        private TableLayoutPanel daysPanel;
        private CheckBox[] dayCheckBoxes;
        private List<(int startHour, int startMinute, int endHour, int endMinute)> timeSlots = new();
        private CheckBox chkAllPlatforms;
        private CheckBox[] platformCheckBoxes;
        private TableLayoutPanel platformPanel;
        private Label lblTotalDuration;
        private Button btnCalculateDuration;
        private Label lblTimeSlotsSummary;

        public AdvertisingForm()
        {
            advertisingService = new AdvertisingService(new SQLiteAdvertisingRepository());
            toast = new ToastNotification(this);

            // Initialize LanguageManager if not already done
            if (LanguageManager.GetActiveLanguagesAsync().Result == null)
            {
                var languageService = new LanguageService(new SQLiteLanguageRepository());
                LanguageManager.Initialize(languageService);
            }

            InitializeComponent();
        }

        private async void InitializeComponent()
        {
            this.Text = "Advertisement Management";
            this.Size = new Size(1100, 700);
            this.StartPosition = FormStartPosition.CenterScreen;
            this.WindowState = FormWindowState.Maximized;

            splitContainer = new SplitContainer();
            splitContainer.Dock = DockStyle.Fill;
            splitContainer.Orientation = Orientation.Vertical;
            splitContainer.SplitterDistance = 600;

            // DataGridView for advertisements
            dgvAdvertising = new DataGridView();
            dgvAdvertising.Dock = DockStyle.Fill;
            dgvAdvertising.SelectionMode = DataGridViewSelectionMode.FullRowSelect;
            dgvAdvertising.MultiSelect = false;
            dgvAdvertising.AllowUserToAddRows = false;
            dgvAdvertising.AllowUserToDeleteRows = false;
            dgvAdvertising.ReadOnly = true;
            dgvAdvertising.AutoGenerateColumns = false;
            dgvAdvertising.RowHeadersVisible = false;
            dgvAdvertising.ColumnHeadersHeightSizeMode = DataGridViewColumnHeadersHeightSizeMode.AutoSize;
            UIStyler.ApplyDataGridViewStyle(dgvAdvertising);

            // Create columns manually with correct DataPropertyNames
            dgvAdvertising.Columns.Clear();
            dgvAdvertising.Columns.Add(new DataGridViewTextBoxColumn { Name = "AnnType", HeaderText = "Type", DataPropertyName = "Ann_Type", Width = 80 });
            dgvAdvertising.Columns.Add(new DataGridViewTextBoxColumn { Name = "AdverName", HeaderText = "Name", DataPropertyName = "Adver_Name", Width = 150 });
            dgvAdvertising.Columns.Add(new DataGridViewTextBoxColumn { Name = "Platform", HeaderText = "Platform", DataPropertyName = "Platform", Width = 80 });
            dgvAdvertising.Columns.Add(new DataGridViewTextBoxColumn { Name = "TimeSlot", HeaderText = "Time Slot", DataPropertyName = "TimeSlot", Width = 120 });
            dgvAdvertising.Columns.Add(new DataGridViewTextBoxColumn { Name = "TrainNumber", HeaderText = "Train No.", DataPropertyName = "TrainNumber", Width = 100 });
            dgvAdvertising.Columns.Add(new DataGridViewTextBoxColumn { Name = "PlayPosition", HeaderText = "Play Position", DataPropertyName = "PlayPosition", Width = 100 });
            dgvAdvertising.Columns.Add(new DataGridViewTextBoxColumn { Name = "Rank", HeaderText = "Rank", DataPropertyName = "Rank", Width = 50 });
            var randomizeColumn = new DataGridViewTextBoxColumn { Name = "Randomize", HeaderText = "Randomize", DataPropertyName = "Randomize", Width = 80 };
            dgvAdvertising.Columns.Add(randomizeColumn);
            dgvAdvertising.Columns.Add(new DataGridViewTextBoxColumn { Name = "Quota", HeaderText = "Quota", DataPropertyName = "Quota", Width = 120 });
            dgvAdvertising.Columns.Add(new DataGridViewTextBoxColumn { Name = "QuotaUsed", HeaderText = "Quota Used", DataPropertyName = "QuotaUsed", Width = 80 });
            dgvAdvertising.Columns.Add(new DataGridViewTextBoxColumn { Name = "Languages", HeaderText = "Languages", DataPropertyName = "Languages", Width = 120 });
            dgvAdvertising.Columns.Add(new DataGridViewTextBoxColumn { Name = "TotalDuration", HeaderText = "Duration", DataPropertyName = "TotalDurationFormatted", Width = 80 });
            dgvAdvertising.Columns.Add(new DataGridViewTextBoxColumn { Name = "Days", HeaderText = "Days", DataPropertyName = "Days", Width = 100, Visible = false });
            // Add Play button column
            var playButtonColumn = new DataGridViewButtonColumn
            {
                Name = "PlayAudio",
                HeaderText = "Play",
                Text = "Play",
                UseColumnTextForButtonValue = true,
                Width = 60
            };
            dgvAdvertising.Columns.Add(playButtonColumn);

            // Add CellFormatting event to handle Randomize column display
            dgvAdvertising.CellFormatting += DgvAdvertising_CellFormatting;
            // Add CellContentClick event to handle Play button
            dgvAdvertising.CellContentClick += DgvAdvertising_CellContentClick;

            // Buttons panel
            var buttonPanel = new FlowLayoutPanel { Dock = DockStyle.Top, Height = 60, FlowDirection = FlowDirection.LeftToRight, Padding = new Padding(12, 12, 0, 0) };
            btnAddAdvertising = new Button { Text = "Add", Width = 100 };
            ButtonStyler.ApplyStandardStyle(btnAddAdvertising, "primary");
            btnAddAdvertising.Click += BtnAddAdvertising_Click;
            btnEditAdvertising = new Button { Text = "Edit", Width = 100 };
            ButtonStyler.ApplyStandardStyle(btnEditAdvertising, "secondary");
            btnEditAdvertising.Click += BtnEditAdvertising_Click;
            btnDeleteAdvertising = new Button { Text = "Delete", Width = 100 };
            ButtonStyler.ApplyStandardStyle(btnDeleteAdvertising, "danger");
            btnDeleteAdvertising.Click += BtnDeleteAdvertising_Click;
            btnRefresh = new Button { Text = "Refresh", Width = 100 };
            ButtonStyler.ApplyStandardStyle(btnRefresh, "info");
            btnRefresh.Click += (s, e) => LoadAdvertisingData();
            buttonPanel.Controls.AddRange(new Control[] { btnAddAdvertising, btnEditAdvertising, btnDeleteAdvertising, btnRefresh });

            // Left panel layout
            var leftPanel = new Panel { Dock = DockStyle.Fill };
            leftPanel.Controls.Add(dgvAdvertising);
            leftPanel.Controls.Add(buttonPanel);

            // Add left panel to split container
            splitContainer.Panel1.Controls.Add(leftPanel);

            // Details panel (right)
            var scrollablePanel = new Panel { Dock = DockStyle.Fill, AutoScroll = true, Padding = new Padding(0), Width = 400 };
            var detailsLayout = new TableLayoutPanel { Dock = DockStyle.Top, ColumnCount = 1, AutoSize = true, AutoSizeMode = AutoSizeMode.GrowAndShrink, Padding = new Padding(16), CellBorderStyle = TableLayoutPanelCellBorderStyle.None };
            int detailsRow = 0;

            // Mode label
            lblMode = new Label { Text = "Add New Advertisement", Font = new Font(Font, FontStyle.Bold), AutoSize = true, Padding = new Padding(0, 8, 0, 4) };
            UIStyler.ApplyLabelStyle(lblMode, "h4");
            detailsLayout.Controls.Add(lblMode, 0, detailsRow++);

            // Section 1: Basic Details
            var lblBasicDetails = new Label { Text = "Basic Details", Font = new Font(Font, FontStyle.Bold), AutoSize = true, Padding = new Padding(0, 0, 0, 0) };
            detailsLayout.Controls.Add(lblBasicDetails, 0, detailsRow++);
            var basicDetailsTable = LayoutHelper.CreateSingleColumnLayout(5);
            basicDetailsTable.AutoSize = true;
            basicDetailsTable.AutoSizeMode = AutoSizeMode.GrowAndShrink;
            basicDetailsTable.Dock = DockStyle.Top;
            int row = 0;
            // Type
            var typeTable = new TableLayoutPanel { ColumnCount = 1, RowCount = 2, AutoSize = true, AutoSizeMode = AutoSizeMode.GrowAndShrink };
            typeTable.Controls.Add(new Label { Text = "Type:", Anchor = AnchorStyles.Left, AutoSize = true }, 0, 0);
            cmbAnnType = new ComboBox { DropDownStyle = ComboBoxStyle.DropDownList, Width = 180, Anchor = AnchorStyles.Left };
            cmbAnnType.Items.AddRange(new object[] { "Advertising", "Slogans" });
            cmbAnnType.SelectedIndexChanged += CmbAnnType_SelectedIndexChanged;
            UIStyler.ApplyComboBoxStyle(cmbAnnType);
            typeTable.Controls.Add(cmbAnnType, 0, 1);
            basicDetailsTable.Controls.Add(typeTable, 0, row++);
            // Name
            var nameTable = new TableLayoutPanel { ColumnCount = 1, RowCount = 2, AutoSize = true, AutoSizeMode = AutoSizeMode.GrowAndShrink };
            nameTable.Controls.Add(new Label { Text = "Name:", Anchor = AnchorStyles.Left, AutoSize = true }, 0, 0);
            txtAdverName = new TextBox { Width = 180, Anchor = AnchorStyles.Left };
            UIStyler.ApplyTextBoxStyle(txtAdverName);
            nameTable.Controls.Add(txtAdverName, 0, 1);
            basicDetailsTable.Controls.Add(nameTable, 0, row++);
            // Platform (dynamic)
            var platformTable = new TableLayoutPanel { ColumnCount = 1, RowCount = 2, AutoSize = true, AutoSizeMode = AutoSizeMode.GrowAndShrink };
            platformTable.Controls.Add(new Label { Text = "Platform:", Anchor = AnchorStyles.Left, AutoSize = true }, 0, 0);
            platformPanel = LayoutHelper.CreatePlatformCheckBoxGrid(out chkAllPlatforms, out platformCheckBoxes, GetAvailablePlatforms());
            platformPanel.Dock = DockStyle.Top;
            chkAllPlatforms.CheckedChanged += ChkAllPlatforms_CheckedChanged;
            if (platformCheckBoxes != null)
            {
                foreach (var checkbox in platformCheckBoxes)
                {
                    checkbox.CheckedChanged += PlatformCheckBox_CheckedChanged;
                }
            }
            platformTable.Controls.Add(platformPanel, 0, 1);
            basicDetailsTable.Controls.Add(platformTable, 0, row++);
            // Rank
            var rankTable = new TableLayoutPanel { ColumnCount = 1, RowCount = 2, AutoSize = true, AutoSizeMode = AutoSizeMode.GrowAndShrink };
            rankTable.Controls.Add(new Label { Text = "Rank:", Anchor = AnchorStyles.Left, AutoSize = true }, 0, 0);
            txtRank = new TextBox { Width = 180, Anchor = AnchorStyles.Left };
            UIStyler.ApplyTextBoxStyle(txtRank);
            rankTable.Controls.Add(txtRank, 0, 1);
            basicDetailsTable.Controls.Add(rankTable, 0, row++);
            // Randomize
            var randomizeTable = new TableLayoutPanel { ColumnCount = 1, RowCount = 2, AutoSize = true, AutoSizeMode = AutoSizeMode.GrowAndShrink };
            randomizeTable.Controls.Add(new Label { Text = "Randomize (same rank):", Anchor = AnchorStyles.Left, AutoSize = true }, 0, 0);
            chkRandomize = new CheckBox { Anchor = AnchorStyles.Left };
            UIStyler.ApplyCheckBoxStyle(chkRandomize);
            randomizeTable.Controls.Add(chkRandomize, 0, 1);
            basicDetailsTable.Controls.Add(randomizeTable, 0, row++);
            detailsLayout.Controls.Add(basicDetailsTable, 0, detailsRow++);

            // Section 2: Language Wave Files
            var lblLanguage = new Label { Text = "Language Wave Files", Font = new Font(Font, FontStyle.Bold), AutoSize = true, Padding = new Padding(0, 12, 0, 8) };
            detailsLayout.Controls.Add(lblLanguage, 0, detailsRow++);
            languageWavePanel = new FlowLayoutPanel { Dock = DockStyle.Fill, AutoSize = true, FlowDirection = FlowDirection.TopDown, Padding = new Padding(0, 0, 0, 0) };
            // Load language wave selectors immediately to ensure they're available
            await LoadLanguageWaveSelectors();
            languageWavePanel.Height = 80;
            detailsLayout.Controls.Add(languageWavePanel, 0, detailsRow++);

            // Add duration display and calculate button
            var durationPanel = new FlowLayoutPanel { FlowDirection = FlowDirection.LeftToRight, AutoSize = true, Padding = new Padding(0, 4, 0, 0) };
            lblTotalDuration = new Label { Text = "Total Duration: 00:00", AutoSize = true, Font = new Font(Font, FontStyle.Bold) };
            UIStyler.ApplyLabelStyle(lblTotalDuration, "info");
            btnCalculateDuration = new Button { Text = "Calculate Duration" };
            ButtonStyler.ApplyStandardStyle(btnCalculateDuration, "info", "small");
            btnCalculateDuration.Click += (s, e) => CalculateAndDisplayTotalDuration();
            durationPanel.Controls.Add(lblTotalDuration);
            durationPanel.Controls.Add(btnCalculateDuration);
            detailsLayout.Controls.Add(durationPanel, 0, detailsRow++);

            // Section 3: Train Connection
            var lblTrain = new Label { Text = "Train Connection", Font = new Font(Font, FontStyle.Bold), AutoSize = true, Padding = new Padding(0, 12, 0, 8) };
            detailsLayout.Controls.Add(lblTrain, 0, detailsRow++);
            var trainTable = LayoutHelper.CreateSingleColumnLayout(2);
            trainTable.AutoSize = true;
            trainTable.AutoSizeMode = AutoSizeMode.GrowAndShrink;
            trainTable.Dock = DockStyle.Top;
            // Train Numbers
            trainTable.Controls.Add(new Label { Text = "Train Numbers:", Anchor = AnchorStyles.Left, AutoSize = true }, 0, 0);
            clbTrainNumbers = new CheckedListBox { Dock = DockStyle.Fill, Height = 100, Width = 150 };
            LoadTrainNumbers();
            trainTable.Controls.Add(clbTrainNumbers, 0, 1);
            // Play Position
            trainTable.Controls.Add(new Label { Text = "Play Position:", Anchor = AnchorStyles.Left, AutoSize = true }, 0, 2);
            cmbPlayPosition = new ComboBox { DropDownStyle = ComboBoxStyle.DropDownList, Width = 150, Anchor = AnchorStyles.Left };
            cmbPlayPosition.Items.AddRange(new object[] { "Before", "After", "Both" });
            cmbPlayPosition.SelectedIndex = 0; // Default to "Before"
            UIStyler.ApplyComboBoxStyle(cmbPlayPosition);
            trainTable.Controls.Add(cmbPlayPosition, 0, 3);
            detailsLayout.Controls.Add(trainTable, 0, detailsRow++);

            // Section 4: Quotas & Days (only for Advertising type)
            lblQuota = new Label { Text = "Quotas and Days", Font = new Font(Font, FontStyle.Bold), AutoSize = true, Padding = new Padding(0, 12, 0, 8) };
            detailsLayout.Controls.Add(lblQuota, 0, detailsRow++);
            quotasPanel = new TableLayoutPanel { ColumnCount = 3, RowCount = 1, AutoSize = true, AutoSizeMode = AutoSizeMode.GrowAndShrink, Dock = DockStyle.Top };

            // Create Month Quota table layout with 1 column and 2 rows
            var monthQuotaTable = new TableLayoutPanel { ColumnCount = 1, RowCount = 2, AutoSize = true, AutoSizeMode = AutoSizeMode.GrowAndShrink };
            monthQuotaTable.Controls.Add(new Label { Text = "Month Quota:", Anchor = AnchorStyles.Left, TextAlign = ContentAlignment.MiddleLeft, AutoSize = true }, 0, 0);
            numMonthQuota = new NumericUpDown { Minimum = 0, Maximum = 10000, Value = 0, Width = 100, Anchor = AnchorStyles.Left };
            UIStyler.ApplyNumericUpDownStyle(numMonthQuota, "custom");
            monthQuotaTable.Controls.Add(numMonthQuota, 0, 1);
            quotasPanel.Controls.Add(monthQuotaTable, 0, 0);

            // Create Day Quota table layout with 1 column and 2 rows
            var dayQuotaTable = new TableLayoutPanel { ColumnCount = 1, RowCount = 2, AutoSize = true, AutoSizeMode = AutoSizeMode.GrowAndShrink };
            dayQuotaTable.Controls.Add(new Label { Text = "Day Quota:", Anchor = AnchorStyles.Left, TextAlign = ContentAlignment.MiddleLeft, AutoSize = true }, 0, 0);
            numDayQuota = new NumericUpDown { Minimum = 0, Maximum = 10000, Value = 0, Width = 100, Anchor = AnchorStyles.Left };
            UIStyler.ApplyNumericUpDownStyle(numDayQuota, "custom");
            dayQuotaTable.Controls.Add(numDayQuota, 0, 1);
            quotasPanel.Controls.Add(dayQuotaTable, 1, 0);

            // Create Slot Quota table layout with 1 column and 2 rows
            var slotQuotaTable = new TableLayoutPanel { ColumnCount = 1, RowCount = 2, AutoSize = true, AutoSizeMode = AutoSizeMode.GrowAndShrink };
            slotQuotaTable.Controls.Add(new Label { Text = "Slot Quota:", Anchor = AnchorStyles.Left, TextAlign = ContentAlignment.MiddleLeft, AutoSize = true }, 0, 0);
            numSlotQuota = new NumericUpDown { Minimum = 0, Maximum = 10000, Value = 0, Width = 100, Anchor = AnchorStyles.Left };
            UIStyler.ApplyNumericUpDownStyle(numSlotQuota, "custom");
            slotQuotaTable.Controls.Add(numSlotQuota, 0, 1);
            quotasPanel.Controls.Add(slotQuotaTable, 2, 0);

            detailsLayout.Controls.Add(quotasPanel, 0, detailsRow++);

            daysPanel = LayoutHelper.CreateDaysCheckBoxGrid(out dayCheckBoxes);
            daysPanel.AutoSize = true;
            daysPanel.AutoSizeMode = AutoSizeMode.GrowAndShrink;
            daysPanel.Dock = DockStyle.Top;
            detailsLayout.Controls.Add(daysPanel, 0, detailsRow++);

            // Section 5: Time Slots
            var lblTimeSlots = new Label { Text = "Time Slots", Font = new Font(Font, FontStyle.Bold), AutoSize = true, Padding = new Padding(0, 12, 0, 8) };
            detailsLayout.Controls.Add(lblTimeSlots, 0, detailsRow++);
            var timeSlotTable = LayoutHelper.CreateSingleColumnLayout(1);
            timeSlotTable.AutoSize = true;
            timeSlotTable.AutoSizeMode = AutoSizeMode.GrowAndShrink;
            timeSlotTable.Dock = DockStyle.Top;
            // Remove old timeSlotsPanel and btnAddTimeSlot
            // Add Manage Time Slots button
            var btnManageTimeSlots = new Button { Text = "Manage Time Slots" };
            ButtonStyler.ApplyStandardStyle(btnManageTimeSlots, "primary", "small");
            lblTimeSlotsSummary = new Label { Text = "No slots set", AutoSize = true, Padding = new Padding(4, 4, 0, 0) };
            btnManageTimeSlots.Click += (s, e) =>
            {
                using (var dlg = new TimeSlotsDialog(timeSlots))
                {
                    if (dlg.ShowDialog() == DialogResult.OK)
                    {
                        timeSlots = dlg.GetTimeSlots();
                        lblTimeSlotsSummary.Text = timeSlots.Count == 0 ? "No slots set" : $"{timeSlots.Count} slot(s) set";
                    }
                }
            };
            var timeSlotPanel = new FlowLayoutPanel { FlowDirection = FlowDirection.LeftToRight, AutoSize = true };
            timeSlotPanel.Controls.Add(btnManageTimeSlots);
            timeSlotPanel.Controls.Add(lblTimeSlotsSummary);
            timeSlotTable.Controls.Add(timeSlotPanel, 0, 0);
            detailsLayout.Controls.Add(timeSlotTable, 0, detailsRow++);

            // Save/Cancel buttons
            var buttonPanelDetails = new FlowLayoutPanel { FlowDirection = FlowDirection.LeftToRight, AutoSize = true, Padding = new Padding(0, 12, 0, 0) };
            btnSave = new Button { Text = "Save" };
            ButtonStyler.ApplyStandardStyle(btnSave, "primary");
            btnSave.Click += BtnSave_Click;
            btnCancel = new Button { Text = "Cancel" };
            ButtonStyler.ApplyStandardStyle(btnCancel, "secondary");
            btnCancel.Click += BtnCancel_Click;
            buttonPanelDetails.Controls.Add(btnSave);
            buttonPanelDetails.Controls.Add(btnCancel);
            detailsLayout.Controls.Add(buttonPanelDetails, 0, detailsRow++);

            scrollablePanel.Controls.Add(detailsLayout);
            splitContainer.Panel2.Controls.Add(scrollablePanel);

            dgvAdvertising.SelectionChanged += DgvAdvertising_SelectionChanged;
            ResetDetailsPanel();

            this.Controls.Add(splitContainer);

            // Subscribe to form load event to ensure proper initialization
            this.Load += async (s, e) =>
            {
                // Load data after form is fully initialized
                LoadAdvertisingData();

                // Ensure language wave selectors are loaded
                if (languageWavePanel.Controls.Count == 0)
                {
                    await LoadLanguageWaveSelectors();
                }
            };
        }

        private void LoadAdvertisingData()
        {
            try
            {
                var dataTable = advertisingService.GetAllAdvertisements();

                // Add Languages column if it doesn't exist
                if (!dataTable.Columns.Contains("Languages"))
                {
                    dataTable.Columns.Add("Languages", typeof(string));
                }

                // Add Quota column if it doesn't exist
                if (!dataTable.Columns.Contains("Quota"))
                {
                    dataTable.Columns.Add("Quota", typeof(string));
                }

                // Add QuotaUsed column if it doesn't exist
                if (!dataTable.Columns.Contains("QuotaUsed"))
                {
                    dataTable.Columns.Add("QuotaUsed", typeof(int));
                }

                // Add TotalDurationFormatted column if it doesn't exist
                if (!dataTable.Columns.Contains("TotalDurationFormatted"))
                {
                    dataTable.Columns.Add("TotalDurationFormatted", typeof(string));
                }

                // Add Days column if it doesn't exist
                if (!dataTable.Columns.Contains("Days"))
                {
                    dataTable.Columns.Add("Days", typeof(string));
                }

                // Populate language information and quota for each advertisement
                foreach (DataRow row in dataTable.Rows)
                {
                    var languageWaves = advertisingService.GetAdvertisementLanguageWaves(
                        row["Ann_Type"].ToString(),
                        row["Adver_Name"].ToString());

                    var languageList = languageWaves.Keys.ToList();
                    row["Languages"] = string.Join(", ", languageList);

                    // Create combined quota string
                    var monthQuota = row["MonthQuota"]?.ToString() ?? "0";
                    var dayQuota = row["DayQuota"]?.ToString() ?? "0";
                    var slotQuota = row["SlotQuota"]?.ToString() ?? "0";

                    row["Quota"] = $"{monthQuota}/{dayQuota}/{slotQuota}";

                    // Set QuotaUsed value (it should already exist in the database)
                    var quotaUsed = row["QuotaUsed"]?.ToString() ?? "0";
                    row["QuotaUsed"] = int.TryParse(quotaUsed, out int quotaUsedValue) ? quotaUsedValue : 0;

                    // Calculate total duration from wave files
                    var waveFiles = languageWaves.Values.Where(w => !string.IsNullOrEmpty(w)).ToArray();
                    var totalDuration = WaveFileDuration.GetTotalDuration(waveFiles);
                    var formattedDuration = WaveFileDuration.GetFormattedTotalDuration(waveFiles);

                    // Update the database with calculated duration
                    if (totalDuration > 0)
                    {
                        try
                        {
                            advertisingService.UpdateTotalDuration(
                                row["Ann_Type"].ToString(),
                                row["Adver_Name"].ToString(),
                                totalDuration,
                                formattedDuration
                            );
                        }
                        catch (Exception ex)
                        {
                            System.Diagnostics.Debug.WriteLine($"Error updating duration: {ex.Message}");
                        }
                    }

                    // Set the duration in the current row
                    row["TotalDurationFormatted"] = formattedDuration;
                }

                // Debug: Log the number of records found
                System.Diagnostics.Debug.WriteLine($"LoadAdvertisingData: Found {dataTable.Rows.Count} records in database");

                // Debug: Log the columns in the DataTable
                System.Diagnostics.Debug.WriteLine("DataTable columns:");
                foreach (DataColumn col in dataTable.Columns)
                {
                    System.Diagnostics.Debug.WriteLine($"  - {col.ColumnName} ({col.DataType})");
                }

                // Debug: Log each record found
                foreach (DataRow row in dataTable.Rows)
                {
                    System.Diagnostics.Debug.WriteLine($"Record: {row["Ann_Type"]} - {row["Adver_Name"]} - Languages: {row["Languages"]} - Days: {row["Days"]}");
                }

                // Clear the current data source first
                dgvAdvertising.DataSource = null;

                // Set the new data source
                dgvAdvertising.DataSource = dataTable;

                // Force refresh of the grid
                dgvAdvertising.Refresh();

                // Debug: Check if DataGridView is properly parented
                System.Diagnostics.Debug.WriteLine($"DataGridView Parent: {dgvAdvertising.Parent?.Name ?? "null"}");
                System.Diagnostics.Debug.WriteLine($"DataGridView Visible: {dgvAdvertising.Visible}");
                System.Diagnostics.Debug.WriteLine($"DataGridView Width: {dgvAdvertising.Width}, Height: {dgvAdvertising.Height}");
                System.Diagnostics.Debug.WriteLine($"DataGridView DataSource: {dgvAdvertising.DataSource?.GetType().Name ?? "null"}");
                System.Diagnostics.Debug.WriteLine($"DataGridView Column Count: {dgvAdvertising.Columns.Count}");
                System.Diagnostics.Debug.WriteLine($"DataGridView Row Count: {dgvAdvertising.Rows.Count}");

                // Try to force a layout update
                dgvAdvertising.PerformLayout();
                dgvAdvertising.Invalidate();

                // Clear any selection
                dgvAdvertising.ClearSelection();

                // Debug: Log the number of rows in the grid
                System.Diagnostics.Debug.WriteLine($"LoadAdvertisingData: Grid now has {dgvAdvertising.Rows.Count} rows");

                // Debug: Log the DataGridView columns
                System.Diagnostics.Debug.WriteLine("DataGridView columns:");
                foreach (DataGridViewColumn col in dgvAdvertising.Columns)
                {
                    System.Diagnostics.Debug.WriteLine($"  - {col.Name} -> {col.DataPropertyName}");
                }
            }
            catch (Exception ex)
            {
                toast.ShowError($"Error loading advertising data: {ex.Message}");
                System.Diagnostics.Debug.WriteLine($"LoadAdvertisingData Error: {ex.Message}");
            }
        }

        private async void DgvAdvertising_SelectionChanged(object sender, EventArgs e)
        {
            if (dgvAdvertising.SelectedRows.Count == 0)
            {
                ResetDetailsPanel();
                return;
            }
            var row = dgvAdvertising.SelectedRows[0];
            isEditMode = true;

            // Set edit mode message with time slot information if available
            var timeSlotsForMessage = row.Cells["TimeSlot"].Value?.ToString() ?? "";
            if (!string.IsNullOrEmpty(timeSlotsForMessage))
            {
                var slotCount = timeSlotsForMessage.Split(';').Count(s => !string.IsNullOrWhiteSpace(s));
                lblMode.Text = $"Edit Advertisement ({slotCount} time slot{(slotCount != 1 ? "s" : "")})";
            }
            else
            {
                lblMode.Text = "Edit Advertisement";
            }
            cmbAnnType.SelectedItem = row.Cells["AnnType"].Value?.ToString();
            txtAdverName.Text = row.Cells["AdverName"].Value?.ToString();
            // Load Play Position
            var playPosition = row.Cells["PlayPosition"].Value?.ToString() ?? "Before";
            if (cmbPlayPosition.Items.Contains(playPosition))
            {
                cmbPlayPosition.SelectedItem = playPosition;
            }
            else
            {
                cmbPlayPosition.SelectedIndex = 0; // Default to "Before"
            }
            LoadPlatformSelection(row.Cells["Platform"].Value?.ToString());
            txtRank.Text = row.Cells["Rank"].Value?.ToString();
            chkRandomize.Checked = row.Cells["Randomize"]?.Value?.ToString() == "1";

            // Parse quota values from the combined quota column
            var quotaValue = row.Cells["Quota"]?.Value?.ToString() ?? "0/0/0";
            var quotaParts = quotaValue.Split('/');
            if (quotaParts.Length >= 3)
            {
                numMonthQuota.Value = ParseInt(quotaParts[0]);
                numDayQuota.Value = ParseInt(quotaParts[1]);
                numSlotQuota.Value = ParseInt(quotaParts[2]);
            }
            else
            {
                // Fallback to individual columns if they exist
                numMonthQuota.Value = ParseInt(row.Cells["MonthQuota"]?.Value);
                numDayQuota.Value = ParseInt(row.Cells["DayQuota"]?.Value);
                numSlotQuota.Value = ParseInt(row.Cells["SlotQuota"]?.Value);
            }
            // Load train numbers
            var trainNumbers = (row.Cells["TrainNumber"].Value?.ToString() ?? "").Split(',');
            for (int i = 0; i < clbTrainNumbers.Items.Count; i++)
            {
                var trainNo = clbTrainNumbers.Items[i].ToString().Split('-')[0].Trim();
                clbTrainNumbers.SetItemChecked(i, trainNumbers.Contains(trainNo));
            }
            // Load days of week (if stored)
            System.Diagnostics.Debug.WriteLine($"Attempting to access Days column for row: {row.Cells["AnnType"].Value} - {row.Cells["AdverName"].Value}");
            var daysString = row.Cells["Days"]?.Value?.ToString() ?? "";
            System.Diagnostics.Debug.WriteLine($"Days string retrieved: '{daysString}'");
            if (!string.IsNullOrEmpty(daysString))
            {
                var selectedDays = daysString.Split(',').Select(int.Parse).ToList();
                System.Diagnostics.Debug.WriteLine($"Parsed selected days: {string.Join(", ", selectedDays)}");
                for (int i = 0; i < dayCheckBoxes.Length; i++)
                {
                    dayCheckBoxes[i].Checked = selectedDays.Contains(i);
                }
            }
            else
            {
                // Reset all day checkboxes if no days stored
                System.Diagnostics.Debug.WriteLine("No days stored, resetting all day checkboxes");
                foreach (var cb in dayCheckBoxes)
                    cb.Checked = false;
            }
            // Load time slots
            var timeSlotsStr = row.Cells["TimeSlot"].Value?.ToString() ?? "";
            timeSlots.Clear();
            foreach (var slot in timeSlotsStr.Split(';'))
            {
                if (string.IsNullOrWhiteSpace(slot)) continue;
                var parts = slot.Split('-');
                if (parts.Length == 2)
                {
                    var start = parts[0].Split(':');
                    var end = parts[1].Split(':');
                    if (start.Length == 2 && end.Length == 2)
                    {
                        if (int.TryParse(start[0], out int startHour) && int.TryParse(start[1], out int startMinute) &&
                            int.TryParse(end[0], out int endHour) && int.TryParse(end[1], out int endMinute))
                        {
                            timeSlots.Add((startHour, startMinute, endHour, endMinute));
                        }
                    }
                }
            }
            // Load wave files per language
            var languageWaves = advertisingService.GetAdvertisementLanguageWaves(
                row.Cells["AnnType"].Value?.ToString(),
                row.Cells["AdverName"].Value?.ToString());

            // Debug: Log the language waves retrieved
            System.Diagnostics.Debug.WriteLine($"Language waves for {row.Cells["AnnType"].Value} - {row.Cells["AdverName"].Value}:");
            foreach (var kvp in languageWaves)
            {
                System.Diagnostics.Debug.WriteLine($"  {kvp.Key}: {kvp.Value}");
            }

            // Ensure language wave panel is populated before trying to fill values
            if (languageWavePanel.Controls.Count == 0)
            {
                await LoadLanguageWaveSelectors();
            }

            // Debug: Log the controls in language wave panel
            System.Diagnostics.Debug.WriteLine($"Language wave panel has {languageWavePanel.Controls.Count} controls");
            foreach (Control ctrl in languageWavePanel.Controls)
            {
                System.Diagnostics.Debug.WriteLine($"Control: {ctrl.GetType().Name}, Name: {ctrl.Name}");
                if (ctrl is FlowLayoutPanel langPanel)
                {
                    System.Diagnostics.Debug.WriteLine($"  FlowLayoutPanel has {langPanel.Controls.Count} controls");
                    foreach (Control innerCtrl in langPanel.Controls)
                    {
                        System.Diagnostics.Debug.WriteLine($"    Inner control: {innerCtrl.GetType().Name}, Name: {innerCtrl.Name}");
                    }
                }
            }

            // Fill language wave inputs with a more robust approach
            await FillLanguageWaveInputs(languageWaves);

            // Load and display duration
            var waveFiles = languageWaves.Values.Where(w => !string.IsNullOrEmpty(w)).ToArray();
            var formattedDuration = WaveFileDuration.GetFormattedTotalDuration(waveFiles);
            if (lblTotalDuration != null)
            {
                lblTotalDuration.Text = $"Total Duration: {formattedDuration}";
            }

            // Force a layout update to ensure all controls are properly displayed
            languageWavePanel.PerformLayout();
            languageWavePanel.Invalidate();

            editingRow = ((row.DataBoundItem as DataRowView)?.Row) ?? null;

            if (lblTimeSlotsSummary != null)
                lblTimeSlotsSummary.Text = timeSlots.Count == 0 ? "No slots set" : $"{timeSlots.Count} slot(s) set";
        }

        private void BtnAddAdvertising_Click(object sender, EventArgs e)
        {
            dgvAdvertising.ClearSelection();
            ResetDetailsPanel();
        }

        private void BtnSave_Click(object sender, EventArgs e)
        {
            // Validate fields
            if (string.IsNullOrWhiteSpace(txtAdverName.Text) || cmbAnnType.SelectedItem == null)
            {
                toast.ShowError("Please enter a name and select a type.");
                return;
            }

            // Calculate total duration before saving
            CalculateAndDisplayTotalDuration();
            // Gather train numbers
            var selectedTrains = clbTrainNumbers.CheckedItems.Cast<string>().ToList();
            string trainNumbers = string.Join(",", selectedTrains.Select(t => t.Split('-')[0].Trim()));
            // Gather days of week
            var selectedDays = dayCheckBoxes
                .Select((cb, idx) => cb.Checked ? idx : -1)
                .Where(idx => idx >= 0)
                .ToList();
            string daysString = string.Join(",", selectedDays); // 0=All Days, 1=Sunday, ...
            // Gather time slots
            var slotList = new List<string>();
            foreach (var (startHour, startMinute, endHour, endMinute) in timeSlots)
            {
                slotList.Add($"{startHour:D2}:{startMinute:D2}-{endHour:D2}:{endMinute:D2}");
            }
            string timeSlotsString = string.Join(";", slotList);
            // Gather wave files per language
            var waveFiles = new Dictionary<string, string>();
            foreach (Control ctrl in languageWavePanel.Controls)
            {
                if (ctrl is FlowLayoutPanel langPanel)
                {
                    var txt = langPanel.Controls.OfType<TextBox>().FirstOrDefault();
                    if (txt != null)
                    {
                        string langCode = txt.Name.Replace("txtWave_", "");
                        string wavePath = txt.Text.Trim();
                        if (!string.IsNullOrEmpty(wavePath))
                        {
                            waveFiles[langCode] = wavePath;
                            System.Diagnostics.Debug.WriteLine($"Saving wave file for {langCode}: {wavePath}");
                        }
                    }
                }
            }

            if (waveFiles.Count == 0)
            {
                toast.ShowError("Please select at least one language wave file before saving.");
                return;
            }

            System.Diagnostics.Debug.WriteLine($"Saving advertisement with {waveFiles.Count} wave files");
            System.Diagnostics.Debug.WriteLine($"Days: {daysString}");
            System.Diagnostics.Debug.WriteLine($"Time slots: {timeSlotsString}");
            System.Diagnostics.Debug.WriteLine($"Platforms: {GetSelectedPlatforms()}");
            System.Diagnostics.Debug.WriteLine($"Train numbers: {trainNumbers}");

            // Compose call to service (update as per your backend model)
            try
            {
                if (isEditMode && editingRow != null)
                {
                    // Set quota values based on announcement type
                    int monthQuota = 0, dayQuota = 0, slotQuota = 0;
                    if (cmbAnnType.SelectedItem?.ToString() == "Advertising")
                    {
                        monthQuota = (int)numMonthQuota.Value;
                        dayQuota = (int)numDayQuota.Value;
                        slotQuota = (int)numSlotQuota.Value;
                    }

                    advertisingService.UpdateAdvertisementWithLanguages(
                        cmbAnnType.SelectedItem.ToString(),
                        txtAdverName.Text.Trim(),
                        waveFiles,
                        GetSelectedPlatforms(),
                        timeSlotsString,
                        trainNumbers,
                        cmbPlayPosition.SelectedItem?.ToString() ?? "Before",
                        int.TryParse(txtRank.Text.Trim(), out int rankVal) ? rankVal : 1,
                        chkRandomize.Checked ? 1 : 0,
                        monthQuota,
                        0, // weekQuota (removed)
                        dayQuota,
                        slotQuota,
                        0, // extraQuota
                        daysString
                    );
                    toast.ShowSuccess("Advertisement updated successfully!");
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine($"Adding new advertisement: {txtAdverName.Text.Trim()}, Type: {cmbAnnType.SelectedItem}");

                    // Set quota values based on announcement type
                    int monthQuota = 0, dayQuota = 0, slotQuota = 0;
                    if (cmbAnnType.SelectedItem?.ToString() == "Advertising")
                    {
                        monthQuota = (int)numMonthQuota.Value;
                        dayQuota = (int)numDayQuota.Value;
                        slotQuota = (int)numSlotQuota.Value;
                    }

                    advertisingService.AddAdvertisementWithLanguages(
                        cmbAnnType.SelectedItem.ToString(),
                        txtAdverName.Text.Trim(),
                        waveFiles,
                        GetSelectedPlatforms(),
                        timeSlotsString,
                        trainNumbers,
                        cmbPlayPosition.SelectedItem?.ToString() ?? "Before",
                        int.TryParse(txtRank.Text.Trim(), out int rankVal) ? rankVal : 1,
                        chkRandomize.Checked ? 1 : 0,
                        monthQuota,
                        0, // weekQuota (removed)
                        dayQuota,
                        slotQuota,
                        0, // extraQuota
                        daysString
                    );

                    System.Diagnostics.Debug.WriteLine("Advertisement added successfully to database");
                    toast.ShowSuccess("Advertisement added successfully!");
                }
                LoadAdvertisingData();
                ResetDetailsPanel();
            }
            catch (Exception ex)
            {
                toast.ShowError(ex.Message);
            }
        }

        private void BtnCancel_Click(object sender, EventArgs e)
        {
            ResetDetailsPanel();
            dgvAdvertising.ClearSelection();
        }

        private void ResetDetailsPanel()
        {
            isEditMode = false;
            editingRow = null;
            if (lblMode != null) lblMode.Text = "Add New Advertisement";
            if (cmbAnnType != null) cmbAnnType.SelectedIndex = 0;
            if (txtAdverName != null) txtAdverName.Text = "";
            if (cmbPlayPosition != null) cmbPlayPosition.SelectedIndex = 0; // Reset to "Before"
            if (chkAllPlatforms != null) chkAllPlatforms.Checked = false;
            if (platformCheckBoxes != null)
            {
                foreach (var checkbox in platformCheckBoxes)
                {
                    checkbox.Checked = false;
                }
            }
            if (txtRank != null) txtRank.Text = "";
            if (chkRandomize != null) chkRandomize.Checked = false;
            if (numMonthQuota != null) numMonthQuota.Value = 0;
            if (numDayQuota != null) numDayQuota.Value = 0;
            if (numSlotQuota != null) numSlotQuota.Value = 0;

            // Reset all language wave file textboxes
            if (languageWavePanel != null)
            {
                foreach (Control ctrl in languageWavePanel.Controls)
                {
                    if (ctrl is FlowLayoutPanel langPanel)
                    {
                        var txt = langPanel.Controls.OfType<TextBox>().FirstOrDefault();
                        if (txt != null)
                        {
                            txt.Text = "";
                            System.Diagnostics.Debug.WriteLine($"Reset textbox: {txt.Name}");
                        }
                    }
                }
            }

            // Reset train number selection
            if (clbTrainNumbers != null)
            {
                for (int i = 0; i < clbTrainNumbers.Items.Count; i++)
                    clbTrainNumbers.SetItemChecked(i, false);
            }

            // Reset day checkboxes
            if (dayCheckBoxes != null)
                foreach (var cb in dayCheckBoxes)
                    cb.Checked = false;

            // Reset time slots
            timeSlots.Clear();

            // Reset duration label
            if (lblTotalDuration != null) lblTotalDuration.Text = "Total Duration: 00:00";

            // Force a layout update to ensure all controls are properly displayed
            if (languageWavePanel != null)
            {
                languageWavePanel.PerformLayout();
                languageWavePanel.Invalidate();
            }
        }

        private int ParseInt(object value)
        {
            int v = 0;
            int.TryParse(value?.ToString(), out v);
            return v;
        }

        private void BtnHindiWave_Click(object sender, EventArgs e)
        {
            using (OpenFileDialog openFileDialog = new OpenFileDialog())
            {
                openFileDialog.Filter = "Wave files (*.wav)|*.wav|All files (*.*)|*.*";
                openFileDialog.Title = "Select Hindi Wave File";
                string initialDir = System.IO.Path.Combine(Application.StartupPath, "Data", "WAVE", "HINDI", (cmbAnnType.SelectedItem?.ToString() == "Advertising" ? "ADVERTISING" : "SLOGAN"));
                if (!System.IO.Directory.Exists(initialDir)) System.IO.Directory.CreateDirectory(initialDir);
                openFileDialog.InitialDirectory = initialDir;
                if (openFileDialog.ShowDialog() == DialogResult.OK)
                {
                    txtHindiWave.Text = openFileDialog.FileName;
                    string destPath = System.IO.Path.Combine(initialDir, System.IO.Path.GetFileName(openFileDialog.FileName));
                    if (!System.IO.File.Exists(destPath))
                    {
                        System.IO.File.Copy(openFileDialog.FileName, destPath, true);
                    }
                }
            }
        }

        private void BtnEngWave_Click(object sender, EventArgs e)
        {
            using (OpenFileDialog openFileDialog = new OpenFileDialog())
            {
                openFileDialog.Filter = "Wave files (*.wav)|*.wav|All files (*.*)|*.*";
                // openFileDialog.Title = "Select English Wave File";
                string initialDir = System.IO.Path.Combine(Application.StartupPath, "Data", "WAVE", "ENGLISH", (cmbAnnType.SelectedItem?.ToString() == "Advertising" ? "ADVERTISING" : "SLOGAN"));
                if (!System.IO.Directory.Exists(initialDir)) System.IO.Directory.CreateDirectory(initialDir);
                openFileDialog.InitialDirectory = initialDir;
                if (openFileDialog.ShowDialog() == DialogResult.OK)
                {
                    txtEngWave.Text = openFileDialog.FileName;
                    string destPath = System.IO.Path.Combine(initialDir, System.IO.Path.GetFileName(openFileDialog.FileName));
                    if (!System.IO.File.Exists(destPath))
                    {
                        System.IO.File.Copy(openFileDialog.FileName, destPath, true);
                    }
                }
            }
        }

        private void BtnEditAdvertising_Click(object sender, EventArgs e)
        {
            if (dgvAdvertising.SelectedRows.Count == 0)
            {
                toast.ShowError("Please select an item to edit.");
                return;
            }
            var row = dgvAdvertising.SelectedRows[0];
            string currentAnnType = row.Cells["AnnType"].Value?.ToString();
            string currentAdverName = row.Cells["AdverName"].Value?.ToString();
            var dataTable = advertisingService.GetAllAdvertisements();
            var selectedRows = dataTable.Select($"Adver_Name = '{currentAdverName}' AND Ann_Type = '{currentAnnType}'");
            if (selectedRows.Length > 0)
            {
                var dataRow = selectedRows[0];
                string hindiWave = dataRow["Hindi_Wave"].ToString();
                string engWave = dataRow["Eng_Wave"].ToString();
                string adverTime = dataRow["Adver_Time"].ToString();
                string adverCount = dataRow["Adver_Count"].ToString();
                // Remove any AddAdvertisingForm references
            }
            else
            {
                toast.ShowError("Selected item not found in database.");
            }
        }

        private void BtnDeleteAdvertising_Click(object sender, EventArgs e)
        {
            if (dgvAdvertising.SelectedRows.Count == 0)
            {
                toast.ShowError("Please select an item to delete.");
                return;
            }
            var row = dgvAdvertising.SelectedRows[0];
            string currentAnnType = row.Cells["AnnType"].Value?.ToString();
            string currentAdverName = row.Cells["AdverName"].Value?.ToString();
            DialogResult confirmResult = MessageBox.Show("Are you sure you want to delete this item?", "Confirm Delete", MessageBoxButtons.YesNo, MessageBoxIcon.Question);
            if (confirmResult == DialogResult.Yes)
            {
                try
                {
                    advertisingService.DeleteAdvertisement(currentAnnType, currentAdverName);
                    toast.ShowSuccess("Item deleted successfully!");
                    LoadAdvertisingData();
                }
                catch (Exception ex)
                {
                    toast.ShowError(ex.Message);
                }
            }
        }

        private async Task LoadLanguageWaveSelectors()
        {
            if (languageWavePanel == null) return;

            languageWavePanel.Controls.Clear();
            var languages = await IPIS.Utils.LanguageManager.GetActiveLanguagesAsync();

            System.Diagnostics.Debug.WriteLine($"LoadLanguageWaveSelectors: Found {languages.Count} active languages");

            foreach (var lang in languages)
            {
                System.Diagnostics.Debug.WriteLine($"Creating selector for language: {lang.Name} ({lang.Code})");

                var langPanel = new FlowLayoutPanel { FlowDirection = FlowDirection.LeftToRight, AutoSize = true };
                var lbl = new Label { Text = $"{lang.Name} Wave File:", AutoSize = true };
                var txtWave = new TextBox { Width = 200, Name = $"txtWave_{lang.Code}" };
                UIStyler.ApplyTextBoxStyle(txtWave);
                var btnBrowse = new Button { Text = "Select" };
                ButtonStyler.ApplyStandardStyle(btnBrowse, "default", "small", null, 25, new Padding(0, 0, 0, 0));
                btnBrowse.Click += (s, e) => BrowseWaveFile(txtWave, lang.WaveFolderPath);
                txtWave.PlaceholderText = lang.Name;
                txtWave.TextChanged += (s, e) => CalculateAndDisplayTotalDuration();
                // langPanel.Controls.Add(lbl);
                langPanel.Controls.Add(txtWave);
                langPanel.Controls.Add(btnBrowse);
                languageWavePanel.Controls.Add(langPanel);

                System.Diagnostics.Debug.WriteLine($"Added textbox with name: txtWave_{lang.Code}");
            }

            System.Diagnostics.Debug.WriteLine($"LoadLanguageWaveSelectors: Added {languageWavePanel.Controls.Count} language panels");
        }

        private async Task FillLanguageWaveInputs(Dictionary<string, string> languageWaves)
        {
            if (languageWavePanel == null) return;

            // Ensure language wave panel is populated
            if (languageWavePanel.Controls.Count == 0)
            {
                await LoadLanguageWaveSelectors();
            }

            // Wait a bit to ensure controls are fully created
            await Task.Delay(100);

            System.Diagnostics.Debug.WriteLine($"FillLanguageWaveInputs: Filling {languageWaves.Count} language waves");

            foreach (Control ctrl in languageWavePanel.Controls)
            {
                if (ctrl is FlowLayoutPanel langPanel)
                {
                    var txt = langPanel.Controls.OfType<TextBox>().FirstOrDefault();
                    if (txt != null)
                    {
                        string langCode = txt.Name.Replace("txtWave_", "");
                        System.Diagnostics.Debug.WriteLine($"Looking for language code: {langCode}");

                        // Try exact match first
                        if (languageWaves.ContainsKey(langCode))
                        {
                            txt.Text = languageWaves[langCode];
                            System.Diagnostics.Debug.WriteLine($"Set {langCode} to: {languageWaves[langCode]} (exact match)");
                        }
                        // Try case-insensitive matching
                        else
                        {
                            var matchingKey = languageWaves.Keys.FirstOrDefault(k =>
                                k.Equals(langCode, StringComparison.OrdinalIgnoreCase));

                            if (matchingKey != null)
                            {
                                txt.Text = languageWaves[matchingKey];
                                System.Diagnostics.Debug.WriteLine($"Set {langCode} to: {languageWaves[matchingKey]} (case-insensitive match: {matchingKey})");
                            }
                            else
                            {
                                txt.Text = "";
                                System.Diagnostics.Debug.WriteLine($"No wave file found for {langCode}");
                            }
                        }
                    }
                }
            }

            // Force a layout update to ensure all controls are properly displayed
            languageWavePanel.PerformLayout();
            languageWavePanel.Invalidate();
        }

        private void BrowseWaveFile(TextBox targetTextBox, string initialDir)
        {
            using (OpenFileDialog openFileDialog = new OpenFileDialog())
            {
                openFileDialog.Filter = "Wave files (*.wav)|*.wav|All files (*.*)|*.*";
                openFileDialog.Title = "Select Wave File";

                // Set initial directory
                string fullInitialDir = Path.Combine(Application.StartupPath, initialDir);
                if (!string.IsNullOrEmpty(initialDir) && Directory.Exists(fullInitialDir))
                    openFileDialog.InitialDirectory = fullInitialDir;
                else if (Directory.Exists(initialDir))
                    openFileDialog.InitialDirectory = initialDir;

                if (openFileDialog.ShowDialog() == DialogResult.OK)
                {
                    // Get the selected file path
                    string selectedFilePath = openFileDialog.FileName;
                    string fileName = Path.GetFileName(selectedFilePath);

                    // Determine the destination directory based on announcement type and language
                    string announcementType = cmbAnnType.SelectedItem?.ToString() ?? "Advertising";

                    // Extract language folder from initialDir (e.g., "Data\WAVE\HINDI" -> "HINDI")
                    string languageFolder = "ENGLISH"; // default
                    if (!string.IsNullOrEmpty(initialDir))
                    {
                        var pathParts = initialDir.Split(Path.DirectorySeparatorChar, Path.AltDirectorySeparatorChar);
                        if (pathParts.Length > 0)
                        {
                            // Find the language folder (HINDI, ENGLISH, etc.)
                            foreach (var part in pathParts)
                            {
                                if (part.Equals("HINDI", StringComparison.OrdinalIgnoreCase) ||
                                    part.Equals("ENGLISH", StringComparison.OrdinalIgnoreCase) ||
                                    part.Equals("GUJARATI", StringComparison.OrdinalIgnoreCase) ||
                                    part.Equals("TAMIL", StringComparison.OrdinalIgnoreCase) ||
                                    part.Equals("MARATHI", StringComparison.OrdinalIgnoreCase) ||
                                    part.Equals("BENGALI", StringComparison.OrdinalIgnoreCase) ||
                                    part.Equals("TELUGU", StringComparison.OrdinalIgnoreCase) ||
                                    part.Equals("KANNADA", StringComparison.OrdinalIgnoreCase) ||
                                    part.Equals("MALAYALAM", StringComparison.OrdinalIgnoreCase) ||
                                    part.Equals("PUNJABI", StringComparison.OrdinalIgnoreCase) ||
                                    part.Equals("URDU", StringComparison.OrdinalIgnoreCase))
                                {
                                    languageFolder = part.ToUpper();
                                    break;
                                }
                            }
                        }
                    }

                    string destinationDir = Path.Combine(Application.StartupPath, "Data", "WAVE", languageFolder, announcementType.ToUpper());

                    // Create directory if it doesn't exist
                    if (!Directory.Exists(destinationDir))
                    {
                        Directory.CreateDirectory(destinationDir);
                    }

                    // Copy file to destination if it doesn't exist
                    string destinationPath = Path.Combine(destinationDir, fileName);
                    if (!File.Exists(destinationPath))
                    {
                        try
                        {
                            File.Copy(selectedFilePath, destinationPath, true);
                        }
                        catch (Exception ex)
                        {
                            System.Diagnostics.Debug.WriteLine($"Error copying file: {ex.Message}");
                            toast.ShowError($"Error copying file: {ex.Message}");
                            return;
                        }
                    }

                    // Save relative path to the textbox
                    string relativePath = Path.Combine("Data", "WAVE", languageFolder, announcementType.ToUpper(), fileName);
                    targetTextBox.Text = relativePath;

                    // Calculate and display duration
                    var duration = WaveFileDuration.GetDuration(destinationPath);
                    var formattedDuration = WaveFileDuration.GetFormattedDuration(destinationPath);

                    if (duration > 0)
                    {
                        // Show duration in a tooltip or status message
                        toast.ShowInfo($"Selected: {fileName} (Duration: {formattedDuration})");
                    }
                }
            }
        }

        private string[] GetAvailablePlatforms()
        {
            var platforms = new List<string>();
            var stationService = new IPIS.Services.StationService(new IPIS.Repositories.SQLiteStationRepository());
            var currentStation = stationService.GetCurrentStation();
            if (currentStation != null && currentStation.AvailablePF != null)
            {
                var platformList = currentStation.AvailablePF.ToString().Split(',');
                foreach (var pf in platformList)
                {
                    var pfTrim = pf.Trim();
                    if (!string.IsNullOrEmpty(pfTrim))
                    {
                        // If the value is a number, generate platform numbers from 1 to that number
                        if (int.TryParse(pfTrim, out int platformCount))
                        {
                            for (int i = 1; i <= platformCount; i++)
                            {
                                platforms.Add(i.ToString());
                            }
                        }
                        else
                        {
                            // If it's not a number, add it as is
                            platforms.Add(pfTrim);
                        }
                    }
                }
            }
            var result = platforms.ToArray();

            // Debug: Log the platforms being generated
            System.Diagnostics.Debug.WriteLine($"GetAvailablePlatforms: Generated {result.Length} platforms: {string.Join(", ", result)}");

            return result;
        }

        private void ChkAllPlatforms_CheckedChanged(object sender, EventArgs e)
        {
            if (platformCheckBoxes != null)
            {
                foreach (var checkbox in platformCheckBoxes)
                {
                    checkbox.Checked = chkAllPlatforms.Checked;
                }
            }
        }

        private void PlatformCheckBox_CheckedChanged(object sender, EventArgs e)
        {
            if (platformCheckBoxes == null) return;

            // Update "All" checkbox based on individual platform selections
            bool allSelected = platformCheckBoxes.All(cb => cb.Checked);
            chkAllPlatforms.Checked = allSelected;
        }

        private void LoadPlatformSelection(string platformValue)
        {
            if (platformCheckBoxes == null) return;

            // Reset all checkboxes
            foreach (var checkbox in platformCheckBoxes)
            {
                checkbox.Checked = false;
            }
            chkAllPlatforms.Checked = false;

            if (string.IsNullOrEmpty(platformValue)) return;

            // Handle "ALL" case
            if (platformValue.Equals("ALL", StringComparison.OrdinalIgnoreCase))
            {
                chkAllPlatforms.Checked = true;
                foreach (var checkbox in platformCheckBoxes)
                {
                    checkbox.Checked = true;
                }
                return;
            }

            // Handle comma-separated platform values
            var selectedPlatforms = platformValue.Split(',');
            foreach (var platform in selectedPlatforms)
            {
                var platformTrim = platform.Trim();
                for (int i = 0; i < platformCheckBoxes.Length; i++)
                {
                    if (platformCheckBoxes[i].Text.Equals(platformTrim, StringComparison.OrdinalIgnoreCase))
                    {
                        platformCheckBoxes[i].Checked = true;
                        break;
                    }
                }
            }

            // Check if all platforms are selected
            bool allSelected = platformCheckBoxes.All(cb => cb.Checked);
            chkAllPlatforms.Checked = allSelected;
        }

        private string GetSelectedPlatforms()
        {
            if (platformCheckBoxes == null || platformCheckBoxes.Length == 0)
                return "ALL";

            var selectedPlatforms = new List<string>();
            foreach (var checkbox in platformCheckBoxes)
            {
                if (checkbox.Checked)
                {
                    selectedPlatforms.Add(checkbox.Text);
                }
            }

            // If all platforms are selected, return "ALL"
            if (selectedPlatforms.Count == platformCheckBoxes.Length)
                return "ALL";

            // If no platforms are selected, return "ALL" as default
            if (selectedPlatforms.Count == 0)
                return "ALL";

            // Return comma-separated list of selected platforms
            return string.Join(",", selectedPlatforms);
        }

        private void LoadTrainNumbers()
        {
            clbTrainNumbers.Items.Clear();
            var trainService = new IPIS.Services.TrainService(new IPIS.Repositories.SQLiteTrainRepository());
            var trainsTable = trainService.GetAllTrains();
            if (trainsTable is System.Data.DataTable dt)
            {
                // Create a list to sort the train data
                var trainList = new List<(string trainNo, string displayText)>();
                
                foreach (System.Data.DataRow row in dt.Rows)
                {
                    string trainNo = row["Train_No"]?.ToString() ?? "";
                    string trainName = row["Train_NameEng"]?.ToString() ?? "";
                    string displayText = $"{trainNo} - {trainName}";
                    trainList.Add((trainNo, displayText));
                }
                
                // Sort by Train_No (numeric sorting)
                trainList.Sort((a, b) => 
                {
                    // Try to parse as integers for proper numeric sorting
                    if (int.TryParse(a.trainNo, out int aNum) && int.TryParse(b.trainNo, out int bNum))
                    {
                        return aNum.CompareTo(bNum);
                    }
                    // Fallback to string comparison if parsing fails
                    return string.Compare(a.trainNo, b.trainNo, StringComparison.Ordinal);
                });
                
                // Add sorted items to the CheckedListBox
                foreach (var train in trainList)
                {
                    clbTrainNumbers.Items.Add(train.displayText);
                }
            }
        }

        private void CustomizeDataGridViewColumns()
        {
            try
            {
                // Hide columns that are not needed for display
                var columnsToHide = new[] { "Msg_Enable", "ExtraQuota" };
                foreach (var colName in columnsToHide)
                {
                    if (dgvAdvertising.Columns.Contains(colName))
                    {
                        dgvAdvertising.Columns[colName].Visible = false;
                    }
                }

                // Customize column headers
                var headerMappings = new Dictionary<string, string>
                {
                    { "Ann_Type", "Type" },
                    { "Adver_Name", "Name" },
                    { "Hindi_Wave", "Hindi Wave" },
                    { "Eng_Wave", "English Wave" },
                    { "Adver_Time", "Adver Time" },
                    { "Adver_Count", "Adver Count" },
                    { "TimeSlot", "Time Slot" },
                    { "TrainNumber", "Train No." },
                    { "PlayPosition", "Play Position" },
                    { "MonthQuota", "Month Quota" },
                    { "WeekQuota", "Week Quota" },
                    { "DayQuota", "Day Quota" },
                    { "SlotQuota", "Slot Quota" }
                };

                foreach (var mapping in headerMappings)
                {
                    if (dgvAdvertising.Columns.Contains(mapping.Key))
                    {
                        dgvAdvertising.Columns[mapping.Key].HeaderText = mapping.Value;
                    }
                }

                // Set column widths
                var widthMappings = new Dictionary<string, int>
                {
                    { "Ann_Type", 80 },
                    { "Adver_Name", 150 },
                    { "Platform", 80 },
                    { "TimeSlot", 120 },
                    { "TrainNumber", 100 },
                    { "PlayPosition", 100 },
                    { "Rank", 50 },
                    { "Randomize", 80 },
                    { "MonthQuota", 80 },
                    { "WeekQuota", 80 },
                    { "DayQuota", 80 },
                    { "SlotQuota", 80 },
                    { "Hindi_Wave", 100 },
                    { "Eng_Wave", 100 },
                    { "Adver_Time", 80 },
                    { "Adver_Count", 80 }
                };

                foreach (var mapping in widthMappings)
                {
                    if (dgvAdvertising.Columns.Contains(mapping.Key))
                    {
                        dgvAdvertising.Columns[mapping.Key].Width = mapping.Value;
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"CustomizeDataGridViewColumns Error: {ex.Message}");
            }
        }

        private void DgvAdvertising_CellFormatting(object sender, DataGridViewCellFormattingEventArgs e)
        {
            if (e.ColumnIndex >= 0 && e.RowIndex >= 0)
            {
                var column = dgvAdvertising.Columns[e.ColumnIndex];

                // Handle Randomize column
                if (column.Name == "Randomize" && e.Value != null)
                {
                    string value = e.Value.ToString();
                    if (value == "1" || value == "True")
                    {
                        e.Value = "Enable";
                        e.FormattingApplied = true;
                    }
                    else if (value == "0" || value == "False")
                    {
                        e.Value = "Disable";
                        e.FormattingApplied = true;
                    }
                }
            }
        }

        private void CalculateAndDisplayTotalDuration()
        {
            try
            {
                // Gather wave files from all language textboxes
                var waveFiles = new List<string>();
                foreach (Control ctrl in languageWavePanel.Controls)
                {
                    if (ctrl is FlowLayoutPanel langPanel)
                    {
                        var txt = langPanel.Controls.OfType<TextBox>().FirstOrDefault();
                        if (txt != null && !string.IsNullOrEmpty(txt.Text.Trim()))
                        {
                            waveFiles.Add(txt.Text.Trim());
                        }
                    }
                }

                // Calculate total duration
                var totalDuration = WaveFileDuration.GetTotalDuration(waveFiles.ToArray());
                var formattedDuration = WaveFileDuration.GetFormattedTotalDuration(waveFiles.ToArray());

                // Update the label
                if (lblTotalDuration != null)
                {
                    lblTotalDuration.Text = $"Total Duration: {formattedDuration}";
                }

                // Display the total duration
                if (totalDuration > 0)
                {
                    toast.ShowInfo($"Total duration: {formattedDuration}");
                }
                else
                {
                    toast.ShowInfo("No wave files selected");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error calculating total duration: {ex.Message}");
            }
        }

        private async void DgvAdvertising_CellContentClick(object sender, DataGridViewCellEventArgs e)
        {
            if (e.RowIndex >= 0 && dgvAdvertising.Columns[e.ColumnIndex].Name == "PlayAudio")
            {
                var row = dgvAdvertising.Rows[e.RowIndex];
                string annType = row.Cells["AnnType"].Value?.ToString();
                string adverName = row.Cells["AdverName"].Value?.ToString();
                if (string.IsNullOrEmpty(annType) || string.IsNullOrEmpty(adverName))
                {
                    toast.ShowError("Invalid advertisement selection.");
                    return;
                }
                // Get all language wave files for this advertisement
                var languageWaves = advertisingService.GetAdvertisementLanguageWaves(annType, adverName);
                var waveFiles = languageWaves.Values.Where(w => !string.IsNullOrEmpty(w)).ToList();
                if (waveFiles.Count == 0)
                {
                    toast.ShowError("No wave files configured for this advertisement.");
                    return;
                }
                // Play each wave file one by one
                await PlayWaveFilesSequentially(waveFiles);

                // Increment quota usage if this is an Advertising type
                if (annType == "Advertising")
                {
                    try
                    {
                        advertisingService.IncrementQuotaUsed(annType, adverName);
                        System.Diagnostics.Debug.WriteLine($"Incremented quota usage for advertisement: {adverName}");
                        // Refresh the data to show updated quota usage
                        LoadAdvertisingData();
                    }
                    catch (Exception ex)
                    {
                        System.Diagnostics.Debug.WriteLine($"Error incrementing quota usage for {adverName}: {ex.Message}");
                    }
                }
            }
        }

        private void CmbAnnType_SelectedIndexChanged(object sender, EventArgs e)
        {
            // Show/hide quota section based on announcement type
            bool isAdvertising = cmbAnnType.SelectedItem?.ToString() == "Advertising";
            if (lblQuota != null) lblQuota.Visible = isAdvertising;
            if (quotasPanel != null) quotasPanel.Visible = isAdvertising;
        }

        private async Task PlayWaveFilesSequentially(List<string> waveFiles)
        {
            if (waveFiles == null || waveFiles.Count == 0)
                return;
            using (var player = new IPIS.Utils.AudioPlayer())
            {
                var tcs = new TaskCompletionSource<bool>();
                player.SetAudioQueue(waveFiles);
                player.PlaybackCompleted += (s, e) => tcs.TrySetResult(true);
                player.StartPlayback();
                await tcs.Task;
            }
        }
    }
}